---
description:
globs:
alwaysApply: false
---
# 状态同步问题调试指南

## 问题识别

### 症状表现
- 相同的方法调用在不同时机产生不同结果
- 系统状态与预期不符
- 组件内部状态不一致

### 常见原因
1. **多个系统管理同一状态**
2. **异步操作导致的竞态条件**
3. **状态更新时序错误**
4. **缓存状态与实际状态不同步**

## 调试方法

### 1. 调用堆栈分析
```csharp
// 在关键方法中添加调用者信息
var stackTrace = new System.Diagnostics.StackTrace(1, true);
var callerMethod = stackTrace.GetFrame(0)?.GetMethod();
var callerClass = callerMethod?.DeclaringType?.Name ?? "Unknown";
LogF8.Log($"调用者: {callerClass}.{callerMethod?.Name}()");
```

### 2. 状态快照对比
```csharp
// 记录关键状态点
LogF8.Log($"状态检查点 - 内部索引: {internalIndex}, 外部索引: {externalIndex}");
LogF8.Log($"是否移动中: {isMoving}, FSM状态: {currentFSMState}");
```

### 3. 时序验证
```csharp
// 添加时间戳和序列号
private static int operationSequence = 0;
LogF8.Log($"[{DateTime.Now:HH:mm:ss.fff}] 操作序列 {++operationSequence}: 开始状态切换");
```

## 解决策略

### 单一职责原则
- 每个状态只由一个组件负责管理
- 其他组件通过接口请求状态变更，而不是直接修改

### 状态同步机制
```csharp
// 强制同步方法
public void SynchronizeState()
{
    // 从权威数据源同步状态
    this.internalState = GetAuthorityState();
    LogF8.Log($"状态已同步: {this.internalState}");
}
```

### 防护检查
```csharp
// 在关键操作前检查状态一致性
if (!IsStateConsistent())
{
    LogF8.LogWarning("检测到状态不一致，尝试修复...");
    SynchronizeState();
}
```

## 相关工具和技术

### 日志级别使用
- `LogF8.Log()`: 正常操作流程
- `LogF8.LogWarning()`: 状态异常但可恢复
- `LogF8.LogError()`: 严重状态错误

### 条件编译
```csharp
#if UNITY_EDITOR
    // 详细的调试信息只在编辑器中输出
    LogF8.Log($"详细状态信息: {GetDetailedStateInfo()}");
#endif
```

## 预防措施

### 1. 设计阶段
- 明确状态所有权
- 定义状态变更协议
- 设计状态验证机制

### 2. 实现阶段
- 添加状态一致性检查
- 实现防御性编程
- 提供状态恢复机制

### 3. 测试阶段
- 编写状态转换测试
- 验证并发场景
- 测试异常恢复路径

## 经验教训

⚠️ **永远不要假设状态是同步的**

即使在单线程环境中，复杂的组件交互也可能导致状态不同步。始终在关键操作前验证状态一致性。
