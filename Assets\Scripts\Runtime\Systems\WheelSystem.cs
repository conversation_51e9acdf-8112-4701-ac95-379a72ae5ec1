using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QFramework;
using F8Framework.Core;
using QHLC.Events;
using QHLC.FSM;
using QHLC.FSM.Wheel;
using QHLC.Managers;

namespace QHLC
{
    /// <summary>
    /// 转盘系统，负责处理转盘相关的业务逻辑
    /// </summary>
    public class WheelSystem : AbstractSystem
    {
        protected override void OnInit()
        {
            // 系统初始化
        }

        /// <summary>
        /// 开始旋转转盘
        /// </summary>
        public void StartSpinning(WheelResult targetResult)
        {
            var model = this.GetModel<WheelModel>();

            // 如果转盘正在旋转，忽略旋转请求
            if (model.IsSpinning.Value)
            {
                return;
            }

            // 设置目标结果
            model.TargetResult.Value = targetResult;

            // 设置转盘为旋转状态
            model.CurrentState.Value = WheelState.Spinning;
            model.IsSpinning.Value = true;
            model.SpinningCompleted.Value = false;
            model.FlashingCompleted.Value = false;

            // 发送开始旋转事件
            TypeEventSystem.Global.Send(new WheelSpinningStartEvent { TargetResult = targetResult });
        }

        /// <summary>
        /// 开始转盘闪烁
        /// </summary>
        public void StartFlashing(WheelResult targetResult)
        {
            var model = this.GetModel<WheelModel>();

            // 如果转盘正在闪烁，忽略闪烁请求
            if (model.IsFlashing.Value)
            {
                return;
            }

            // 设置目标结果
            model.TargetResult.Value = targetResult;

            // 设置转盘为闪烁状态
            model.CurrentState.Value = WheelState.Flashing;
            model.IsFlashing.Value = true;
            model.FlashingCompleted.Value = false;

            // 发送开始闪烁事件
            TypeEventSystem.Global.Send(new WheelFlashingStartEvent { Result = targetResult });
        }

        /// <summary>
        /// 处理转盘旋转完成
        /// </summary>
        public void OnSpinningCompleted()
        {
            var model = this.GetModel<WheelModel>();

            // 设置旋转完成标志
            model.SpinningCompleted.Value = true;

            // 获取目标结果
            WheelResult targetResult = model.TargetResult.Value;
            LogF8.Log($"WheelSystem.OnSpinningCompleted: 旋转完成，发送转盘停止事件，结果: {targetResult} ({(int)targetResult})");

            // 发送转盘停止事件
            TypeEventSystem.Global.Send(new WheelStoppedEvent { Result = targetResult });

            // 通过GameFSMCoordinator请求切换到闪烁状态，而不是直接发送闪烁事件
            LogF8.Log($"WheelSystem.OnSpinningCompleted: 通过GameFSMCoordinator请求切换到闪烁状态");
            if (GameFSMCoordinator.Instance != null)
            {
                GameFSMCoordinator.Instance.RequestWheelStateChange(WheelStateType.Flashing);
            }
            else
            {
                LogF8.LogError("WheelSystem.OnSpinningCompleted: GameFSMCoordinator单例未找到，无法切换状态");
            }
        }

        /// <summary>
        /// 处理转盘闪烁完成
        /// </summary>
        public void OnFlashingCompleted()
        {
            var model = this.GetModel<WheelModel>();

            // 设置闪烁完成标志
            model.FlashingCompleted.Value = true;

            // 获取目标结果
            WheelResult targetResult = model.TargetResult.Value;
            LogF8.Log($"WheelSystem.OnFlashingCompleted: 闪烁完成，发送转盘闪烁完成事件，结果: {targetResult} ({(int)targetResult})");

            // 发送转盘闪烁完成事件
            TypeEventSystem.Global.Send(new WheelFlashingCompletedEvent { Result = targetResult });

            // 根据转盘结果触发潜艇移动
            int moveSteps = (int)targetResult; // WheelResult枚举值直接对应移动步数
            LogF8.Log($"WheelSystem.OnFlashingCompleted: 转盘闪烁完成，准备直接调用SubmarineManager移动，步数: {moveSteps}");

            // 🔧 修复：移除提前的状态切换，让SubmarineManager自己管理状态切换时序
            // 这样可以确保与SubmarineManagerTest产生一致的正确效果
            // 原来的代码：
            // if (GameFSMCoordinator.Instance != null)
            // {
            //     GameFSMCoordinator.Instance.ChangeState(GameStateType.SubmarineMoving);
            // }

            // 🔧 在移动前检查并确保SubmarineManager状态一致性
            if (SubmarineManager.Instance != null)
            {
                var submarineManager = SubmarineManager.Instance;

                // 检查潜艇是否正在移动
                if (submarineManager.IsMoving())
                {
                    LogF8.LogWarning("WheelSystem.OnFlashingCompleted: 潜艇正在移动中，忽略新的移动请求以避免状态冲突");
                    LogF8.LogWarning("WheelSystem.OnFlashingCompleted: 建议检查游戏流程，确保转盘和潜艇移动不会重叠");
                    return;
                }

                LogF8.Log($"WheelSystem.OnFlashingCompleted: SubmarineManager状态检查完成，当前站点: {submarineManager.GetCurrentStationIndex()}");
            }
            else
            {
                LogF8.LogError("WheelSystem.OnFlashingCompleted: SubmarineManager单例未找到");
                return;
            }

            // 🔧 直接调用SubmarineManager移动，让其自己管理状态切换
            // 这样确保状态切换时序正确：先设置isMoving=true，再切换FSM状态
            LogF8.Log($"WheelSystem.OnFlashingCompleted: 直接调用SubmarineManager.MoveSubmarineStatic，步数: {moveSteps}");
            SubmarineManager.MoveSubmarineStatic(moveSteps, () => {
                LogF8.Log("WheelSystem.OnFlashingCompleted: 潜艇移动完成回调");
                // 移动完成后可以发送事件通知其他系统
                TypeEventSystem.Global.Send(new SubmarineMoveCompletedEvent());
            });
        }



        /// <summary>
        /// 重置转盘
        /// </summary>
        public void ResetWheel()
        {
            var model = this.GetModel<WheelModel>();

            // 重置转盘状态
            model.CurrentState.Value = WheelState.Idle;
            model.IsSpinning.Value = false;
            model.IsFlashing.Value = false;
            model.SpinningCompleted.Value = false;
            model.FlashingCompleted.Value = false;

            // 发送重置事件
            TypeEventSystem.Global.Send(new WheelResetEvent());
        }

        /// <summary>
        /// 获取随机转盘结果
        /// </summary>
        /// <param name="excludeStop">是否排除Stop结果</param>
        public WheelResult GetRandomWheelResult(bool excludeStop = false)
        {
            LogF8.LogError($"WheelSystem.GetRandomWheelResult: 开始生成随机转盘结果，排除Stop: {excludeStop}");

            var model = this.GetModel<WheelModel>();
            LogF8.LogError($"WheelSystem.GetRandomWheelResult: model = {(model != null ? "not null" : "null")}");

            if (model != null && model.wheelConfig != null)
            {
                LogF8.LogError($"WheelSystem.GetRandomWheelResult: wheelConfig = {(model.wheelConfig != null ? "not null" : "null")}");
                LogF8.LogError("WheelSystem.GetRandomWheelResult: 调用model.wheelConfig.GetRandomResult()");

                WheelResult result = model.wheelConfig.GetRandomResult(excludeStop);
                LogF8.LogError($"WheelSystem.GetRandomWheelResult: 使用概率设置生成随机结果: {result} ({(int)result})");
                return result;
            }
            else
            {
                LogF8.LogError("WheelSystem.GetRandomWheelResult: wheelConfig为空，使用默认随机生成方式");

                if (excludeStop)
                {
                    // 排除Stop结果，只生成Move1-Move4
                    int randomValue = Random.Range(1, 5); // 生成[1,5)范围的随机数，即1,2,3,4
                    WheelResult result = (WheelResult)randomValue;
                    LogF8.LogError($"WheelSystem.GetRandomWheelResult: 生成随机结果(排除Stop): {result} ({randomValue})");
                    return result;
                }
                else
                {
                    // 包含所有结果，包括Stop
                    int randomValue = Random.Range(0, 5); // 生成[0,5)范围的随机数，即0,1,2,3,4
                    WheelResult result = (WheelResult)randomValue;
                    LogF8.LogError($"WheelSystem.GetRandomWheelResult: 生成随机结果(包含Stop): {result} ({randomValue})");
                    return result;
                }
            }
        }
    }
}
