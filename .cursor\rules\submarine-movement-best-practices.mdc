---
description:
globs:
alwaysApply: false
---
# 潜艇移动系统最佳实践

## 核心问题：PathController状态同步

[SubmarineManager.cs](mdc:Assets/Scripts/Runtime/Managers/SubmarineManager.cs) 中存在一个已知的关键问题：**PathController内部状态与currentStationIndex不同步**。这会导致相同的移动调用产生不同的效果。

### 问题表现
- 相同的 `MoveSubmarineStatic(steps)` 调用在不同时机产生不同的移动路径
- PathController 可能从错误的起始位置开始移动
- 导致潜艇移动到预期之外的位置

### 根本原因
```csharp
// SubmarineManager.MoveWithPathController 中的关键代码
ModuleLogManager.LogSubmarineWarning($"PathController可能从不同的内部位置开始移动");
pathController.SetCurrentPosition(currentStationIndex); // 尝试同步状态
```

## 正确的调用方式

### ✅ 推荐做法
```csharp
// 检查状态一致性
if (SubmarineManager.Instance != null)
{
    var submarineManager = SubmarineManager.Instance;
    
    // 防止重复调用
    if (submarineManager.IsMoving())
    {
        LogF8.LogWarning("潜艇正在移动中，忽略新的移动请求");
        return;
    }
    
    // 让SubmarineManager自己管理状态切换时序
    SubmarineManager.MoveSubmarineStatic(moveSteps, onComplete);
}
```

### ❌ 避免的做法
```csharp
// 不要提前切换FSM状态，这会干扰SubmarineManager的内部状态管理
GameFSMCoordinator.Instance.ChangeState(GameStateType.SubmarineMoving);
SubmarineManager.MoveSubmarineStatic(moveSteps, onComplete);
```

## 状态切换时序规则

### 正确的时序
1. 调用 `SubmarineManager.MoveSubmarineStatic()`
2. SubmarineManager 内部设置 `isMoving = true`
3. SubmarineManager 内部切换 FSM 状态到 Moving
4. 执行实际移动逻辑
5. 移动完成后恢复状态

### 错误的时序
1. ❌ 外部提前切换 FSM 状态
2. ❌ 在潜艇还在移动时发起新的移动请求
3. ❌ 跳过状态检查直接调用移动

## 相关文件引用

- 潜艇管理器：[SubmarineManager.cs](mdc:Assets/Scripts/Runtime/Managers/SubmarineManager.cs)
- 转盘系统：[WheelSystem.cs](mdc:Assets/Scripts/Runtime/Systems/WheelSystem.cs)
- 测试脚本：[QuickSubmarineTest.cs](mdc:Assets/Scripts/Runtime/Test/QuickSubmarineTest.cs)

## 调试指导

### 关键日志检查点
1. `SubmarineManager.currentStationIndex` 的值
2. PathController 内部位置状态
3. `isMoving` 标志状态
4. FSM 状态切换时序

### 故障排除步骤
1. 检查是否有多个系统同时调用移动
2. 验证状态切换时序是否正确
3. 确认 PathController 状态同步是否成功
4. 检查移动完成回调是否正确执行

## 重要提醒

⚠️ **切勿在外部管理 SubmarineManager 的 FSM 状态切换！**

SubmarineManager 有复杂的内部状态管理逻辑，外部状态切换会破坏其内部一致性，导致移动行为不可预测。

正确做法是让 SubmarineManager 完全自主管理其状态生命周期。
