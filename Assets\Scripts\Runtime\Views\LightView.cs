using UnityEngine;
using QFramework;
using QHLC.Models;
using QHLC.Events;
using System.Collections;
using System.Collections.Generic;
using F8Framework.Core;
using QHLC.Utilities;
using DG.Tweening;
using F8Framework.Launcher; // 添加DOTween命名空间

namespace QHLC.Views
{
    /// <summary>
    /// 灯光视图，负责灯光控制的视觉效果
    /// </summary>
    public class LightView : MonoBehaviour, IController, ICanSendEvent
    {
        [Header("灯光设置")][SerializeField] private GameObject[] lightObjects; // 灯光游戏对象数组
        [Header("音效和特效")]
        [SerializeField] private AudioClip lightActivationSound; // 灯光激活音效
        [SerializeField] private ParticleSystem lightActivationEffect; // 灯光激活特效
        [SerializeField] private GameObject lightActivationAnimationPrefab;//灯光激活动画预设
        private bool[] activatedLights; // 记录哪些灯已被激活
        private SimpleObjectPool<GameObject> lightActivationAnimationPool; // 灯光激活动画对象池

        // 注册事件监听
        private void RegisterEvents()
        {
            // 注册随机灯光激活事件
            TypeEventSystem.Global.Register<LightActivatedEvent>(OnLightActivatedEvent);
        }

        // 取消事件监听
        private void UnregisterEvents()
        {
            TypeEventSystem.Global.UnRegister<LightActivatedEvent>(OnLightActivatedEvent);
        }

        private void Awake()
        {
            // 初始化激活状态数组
            activatedLights = new bool[lightObjects.Length];

            // 更新LightModel中的总灯光数
            var lightModel = this.GetModel<LightModel>();
            // 使用配置中的总灯光数，而不是使用requiredLightsCount
            // lightModel.TotalLights.Value = requiredLightsCount;
            //创建lightActivationAnimationPrefab对象池
            if (lightActivationAnimationPrefab != null)
            {
                lightActivationAnimationPool = new SimpleObjectPool<GameObject>(() =>
                {
                    var gameObj = GameObject.Instantiate(lightActivationAnimationPrefab);
                    gameObj.transform.SetParent(transform);
                    gameObj.SetActive(false);
                    return gameObj;
                }, (gameObj) =>
                {
                    // reset code here
                    gameObj.SetActive(false);
                }, 5);

            }
            // 注册事件监听
            RegisterEvents();

            ResetLights();
        }

        private void OnDestroy()
        {
            // 取消事件监听
            UnregisterEvents();
        }


        // 处理空格键输入
        public void OnSpaceKeyDown()
        {
            // 尝试激活一个随机灯光，并触发事件
            int lightIndex = GetNextUnactivatedLightIndex();
            if (lightIndex >= 0)
            {
                // 发送灯光激活事件
                TypeEventSystem.Global.Send(new LightActivatedEvent { LightIndex = lightIndex });
            }
        }
        public void OnLightActivedAnimationCompleted(int index)
        {
            // 发送灯光动画完成事件

        }


        // 处理灯光激活事件
        private void OnLightActivatedEvent(LightActivatedEvent evt)
        {
            // 打印调试信息
            ModuleLogManager.LogLight($"LightView: 收到灯光激活事件，索引: {evt.LightIndex}");

            // 如果是自动灯光激活事件（索引为-1）
            if (evt.LightIndex == -1)
            {
                // 尝试激活下一个未激活的灯光（按顺序）
                int lightIndex = GetNextUnactivatedLightIndex();
                ModuleLogManager.LogLight($"LightView: 按顺序选择灯光索引: {lightIndex}");

                if (lightIndex >= 0)
                {
                    // 激活指定索引的灯光
                    ActivateLightAtIndex(lightIndex);
                }
                else
                {
                    ModuleLogManager.LogLight("LightView: 无法找到可激活的灯光");
                }
            }
            else
            {
                // 如果是指定索引的灯光激活事件
                ActivateLightAtIndex(evt.LightIndex);
            }
        }

        // 获取下一个未激活的灯光索引（按顺序）
        public int GetNextUnactivatedLightIndex()
        {
            var lightModel = this.GetModel<LightModel>();

            // 检查是否所有灯都已激活
            if (lightModel.AllLightsActivated.Value)
            {
                return -1; // 如果所有灯都已激活，返回-1表示失败
            }

            // 从索引0开始，依次检查每个灯光是否已激活
            for (int i = 0; i < activatedLights.Length; i++)
            {
                if (!activatedLights[i])
                {
                    ModuleLogManager.LogLight($"LightView: 找到下一个未激活的灯光，索引: {i}");
                    return i; // 返回第一个未激活的灯光索引
                }
            }

            ModuleLogManager.LogLight("LightView: 所有灯光都已激活，但LightModel.AllLightsActivated为false");
            return -1; // 如果没有找到未激活的灯光，返回-1
        }

        // 等待潜艇停止移动
        private IEnumerator WaitForSubmarineToStopMoving()
        {
            var submarineModel = this.GetModel<SubmarineModel>();

            // 等待潜艇停止移动
            while (submarineModel.IsMoving.Value)
            {
                //LogF8.Log("LightView: 等待潜艇停止移动...");
                yield return new WaitForSeconds(0.5f);
            }

            // 潜艇停止移动后，发送所有灯光激活事件
            ModuleLogManager.LogLight("LightView: 潜艇停止移动，发送AllLightsActivatedEvent事件");
            TypeEventSystem.Global.Send(new QHLC.Events.AllLightsActivatedEvent());
        }

        // 尝试激活一个随机灯光
        public int TryActivateRandomLight()
        {
            var lightModel = this.GetModel<LightModel>();

            // 检查是否所有灯都已激活
            if (lightModel.AllLightsActivated.Value)
            {
                return -1; // 如果所有灯都已激活，返回-1表示失败
            }

            // 移除随机概率，确保每次都激活一个灯
            // 随机选择一个未激活的灯
            int randomIndex;
            int attempts = 0;
            int maxAttempts = lightObjects.Length * 2; // 防止无限循环

            do
            {
                randomIndex = Random.Range(0, lightObjects.Length);
                attempts++;
            } while (activatedLights[randomIndex] && attempts < maxAttempts);

            // 如果找到了未激活的灯或者尝试次数过多
            if (!activatedLights[randomIndex] || attempts >= maxAttempts)
            {
                return randomIndex; // 返回找到的灯光索引，但不激活它
            }

            return -1; // 如果没有找到未激活的灯光，返回-1
        }

        // 激活指定索引的灯
        public void ActivateLightAtIndex(int index)
        {
            if (index < 0 || index >= lightObjects.Length)
            {
                ModuleLogManager.LogLight($"LightView: 尝试激活无效的灯光索引: {index}");
                return;
            }

            // 设置灯为激活状态
            lightObjects[index].SetActive(true);
            var lightActivationAnimation = lightActivationAnimationPool.Allocate();
            lightActivationAnimation.SetActive(true);
            lightActivationAnimation.transform.SetParent(lightObjects[index].transform);
            lightActivationAnimation.transform.localPosition = Vector3.zero;
            activatedLights[index] = true;

            // 更新模型中的当前灯光索引
            var lightModel = this.GetModel<LightModel>();
            int activatedCount = GetActivatedLightCount();
            lightModel.CurrentLightIndex.Value = activatedCount;

            // 检查是否所有灯光都已激活
            if (activatedCount >= lightModel.TotalLights.Value)
            {
                ModuleLogManager.LogLight($"LightView: 所有灯光已激活，当前激活数: {activatedCount}, 总数: {lightModel.TotalLights.Value}");

                // 检查潜艇是否正在移动
                var submarineModel = this.GetModel<SubmarineModel>();
                if (submarineModel.IsMoving.Value)
                {
                    ModuleLogManager.LogLight("LightView: 潜艇正在移动，延迟发送AllLightsActivatedEvent事件");
                    // 如果潜艇正在移动，不发送事件，等待潜艇移动完成
                    StartCoroutine(WaitForSubmarineToStopMoving());
                }
                else
                {
                    // 触发所有灯光激活事件
                    ModuleLogManager.LogLight("LightView: 所有灯光已激活，发送AllLightsActivatedEvent事件");
                    TypeEventSystem.Global.Send(new QHLC.Events.AllLightsActivatedEvent());
                }
            }

            // 播放音效
            if (lightActivationSound != null)
            {
                AudioSource.PlayClipAtPoint(lightActivationSound, Camera.main.transform.position);
            }

            // 播放特效
            if (lightActivationEffect != null)
            {
                lightActivationEffect.transform.position = lightObjects[index].transform.position;
                lightActivationEffect.Play();
            }
        }



        // 重置所有灯光状态
        public void ResetLights()
        {
            TurnOffAllLights();
            for (int i = 0; i < activatedLights.Length; i++)
            {
                activatedLights[i] = false;
            }

            // 重置模型中的灯光状态
            this.SendCommand<ResetLightsCommand>();
        }

        // 获取当前灯光索引
        public int GetCurrentLightIndex()
        {
            var lightModel = this.GetModel<LightModel>();
            return lightModel.CurrentLightIndex.Value;
        }

        // 获取激活的灯光数量
        public int GetActivatedLightCount()
        {
            int count = 0;
            foreach (bool isActivated in activatedLights)
            {
                if (isActivated) count++;
            }
            ModuleLogManager.LogLight($"LightView: 当前激活的灯光数量: {count}, 总灯光数: {this.GetModel<LightModel>().TotalLights.Value}");
            return count;
        }

        // 关闭所有灯光
        private void TurnOffAllLights()
        {
            foreach (var light in lightObjects)
            {
                light.SetActive(false);
            }
        }

        // 播放灯光全亮动画
        public void PlayAllLightsActivatedAnimation(System.Action onComplete = null)
        {
            ModuleLogManager.LogLight("LightView: 播放灯光全亮动画");

            // 创建一个序列动画
            DG.Tweening.Sequence sequence = DG.Tweening.DOTween.Sequence();

            // 创建一个字典来存储每个灯光对象的原始缩放
            Dictionary<GameObject, Vector3> originalScales = new Dictionary<GameObject, Vector3>();

            // 对每个灯光添加闪烁动画
            foreach (var lightObj in lightObjects)
            {
                if (lightObj != null)
                {
                    // 保存原始缩放
                    Vector3 originalScale = lightObj.transform.localScale;
                    originalScales[lightObj] = originalScale;

                    // 添加缩放动画
                    sequence.Join(
                        lightObj.transform.DOScale(originalScale * 1.2f, 0.3f)
                        .SetLoops(2, DG.Tweening.LoopType.Yoyo)
                    );
                }
            }

            // 设置完成回调
            sequence.OnComplete(() =>
            {
                ModuleLogManager.LogLight("LightView: 灯光全亮动画完成");

                // 手动重置所有灯光对象的缩放
                foreach (var lightObj in lightObjects)
                {
                    if (lightObj != null && originalScales.ContainsKey(lightObj))
                    {
                        lightObj.transform.localScale = originalScales[lightObj];
                    }
                }

                // 只重置灯光视图的显示状态，不重置模型状态
                TurnOffAllLights();
                for (int i = 0; i < activatedLights.Length; i++)
                {
                    activatedLights[i] = false;
                }

                // 发送灯光动画完成事件
                TypeEventSystem.Global.Send(new LightAnimationCompletedEvent());

                // 调用完成回调
                onComplete?.Invoke();
            });

            // 播放动画
            sequence.Play();
        }

        // 只显示指定索引的灯光
        public void ShowLightAtIndex(int index)
        {
            if (index < 0 || index >= lightObjects.Length) return;

            // 在新玩法中，这个方法不会重置其他灯光的状态
            // 只是更新当前索引并确保该灯光显示
            lightObjects[index].SetActive(true);
            activatedLights[index] = true;

            // 更新模型中的当前灯光索引
            var lightModel = this.GetModel<LightModel>();
            int activatedCount = GetActivatedLightCount();
            lightModel.CurrentLightIndex.Value = activatedCount;

            // 检查是否所有灯都已激活
            if (activatedCount >= lightModel.TotalLights.Value)
            {
                // 触发所有灯光激活事件
                TypeEventSystem.Global.Send(new QHLC.Events.AllLightsActivatedEvent());
            }
        }

        /// <summary>
        /// 获取架构
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return QHLCArchitecture.Interface;
        }
    }
}