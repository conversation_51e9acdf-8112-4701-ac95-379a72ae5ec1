using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using QHLC.Controllers;
using QHLC.Models;
using QHLC.Events;
using QHLC.FSM.Wheel;
using QHLC.Managers;
using QFramework;
using F8Framework.Core;
using QHLC.FSM.HLight;

namespace QHLC.FSM
{
    /// <summary>
    /// 游戏状态机协调器，负责协调独立FSM之间的交互
    /// 单例模式，统一管理所有状态切换
    /// </summary>
    public class GameFSMCoordinator : MonoBehaviour, ICanGetSystem
    {
        #region 单例实现
        private static GameFSMCoordinator _instance;
        public static GameFSMCoordinator Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<GameFSMCoordinator>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("GameFSMCoordinator");
                        _instance = go.AddComponent<GameFSMCoordinator>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        [SerializeField] private GameController gameController;

        // 当前游戏状态
        private GameStateType currentState = GameStateType.Idle;

        // 状态变化事件监听
        private bool isInitialized = false;

        private void Awake()
        {
            // 确保单例唯一性
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }
            _instance = this;
            DontDestroyOnLoad(gameObject);

            if (gameController == null)
            {
                gameController = GetComponent<GameController>();
                if (gameController == null)
                {
                    LogF8.LogError("GameFSMCoordinator: GameController not found!");
                    return;
                }
            }

            // 注册FSM状态变化事件监听
            TypeEventSystem.Global.Register<FSMStateChangedEvent>(OnFSMStateChanged);

            // 注册游戏状态切换请求事件监听
            TypeEventSystem.Global.Register<GameStateChangeRequestEvent>(OnGameStateChangeRequest);
        }

        private void Update()
        {
            // 移除输入缓存监控逻辑，这应该由LightHFSM来处理
            // 只有在初始化完成后才进行输入缓存监控
            // if (!isInitialized) return;

            // 监控输入缓存，在Idle状态下检查是否有输入缓存
            // MonitorInputBuffer();
        }

        /// <summary>
        /// 监控输入缓存系统 - 已移除，由LightHFSM处理
        /// </summary>
        // private void MonitorInputBuffer()
        // {
        //     // 确保输入缓存系统引用存在
        //     if (inputBufferSystem == null)
        //     {
        //         inputBufferSystem = gameController.GetSystem<InputBufferSystem>();
        //         if (inputBufferSystem == null)
        //         {
        //             // 输入缓存系统还未初始化，跳过本次检查
        //             return;
        //         }
        //     }

        //     // 只在Idle状态下监控输入缓存
        //     if (currentState == GameStateType.Idle)
        //     {
        //         // 检查是否有缓存的输入
        //         if (inputBufferSystem.HasBufferedInputs())
        //         {
        //             LogF8.Log("GameFSMCoordinator: 检测到输入缓存，开始游戏流程");
        //             StartGame();
        //         }
        //     }
        // }

        /// <summary>
        /// 初始化协调器
        /// </summary>
        public void Initialize()
        {
            if (isInitialized)
            {
                LogF8.LogWarning("GameFSMCoordinator: 已经初始化过，不需要重复初始化");
                return;
            }

            // 设置初始状态
            currentState = GameStateType.Idle;
            isInitialized = true;

            LogF8.Log("GameFSMCoordinator: 初始化完成，现在作为协调器管理各个独立FSM之间的交互");
        }

        /// <summary>
        /// 开始游戏
        /// </summary>
        public void StartGame()
        {
            if (!isInitialized)
            {
                LogF8.LogError("GameFSMCoordinator: 尚未初始化，无法开始游戏");
                return;
            }

            // 切换到灯光闪烁状态，开始游戏流程
            ChangeState(GameStateType.FlashingLights);
        }

        /// <summary>
        /// 处理FSM状态变化事件
        /// </summary>
        private void OnFSMStateChanged(FSMStateChangedEvent evt)
        {
            if (!isInitialized) return;

            LogF8.Log($"GameFSMCoordinator: 收到FSM状态变化事件 - 类型: {evt.FSMType}, 从 {evt.PreviousState} 到 {evt.CurrentState}");

            // 🔧 修复Bug 3：加强状态同步验证
            // 打印所有状态机的当前状态，便于调试和验证
            LogF8.Log($"GameFSMCoordinator: 当前游戏状态: {currentState}");
            // 注意：lightFSM现在作为WheelFSM的嵌套状态机，不再单独显示
            // LogF8.Log($"GameFSMCoordinator: 当前灯光状态: {gameController.lightFSM?.CurrentStateId?.ToString() ?? "未初始化"}");
            LogF8.Log($"GameFSMCoordinator: 当前灯光状态: 由WheelFSM嵌套管理");
            LogF8.Log($"GameFSMCoordinator: 当前转盘状态: {gameController.wheelFSM?.CurrentStateId?.ToString() ?? "未初始化"}");
            LogF8.Log($"GameFSMCoordinator: 当前潜艇状态: {gameController.submarineFSM?.CurrentStateId?.ToString() ?? "未初始化"}");

            // 验证状态一致性
            ValidateStateConsistency(evt);

            // 根据不同FSM的状态变化，协调其他FSM的状态
            switch (evt.FSMType)
            {
                case FSMType.Light:
                    HandleLightFSMStateChanged(evt.PreviousState, evt.CurrentState);
                    break;
                case FSMType.Wheel:
                    HandleWheelFSMStateChanged(evt.PreviousState, evt.CurrentState);
                    break;
                case FSMType.Submarine:
                    HandleSubmarineFSMStateChanged(evt.PreviousState, evt.CurrentState);
                    break;
            }
        }

        /// <summary>
        /// 验证状态一致性
        /// </summary>
        private void ValidateStateConsistency(FSMStateChangedEvent evt)
        {
            // 检查状态机引用是否有效
            // 注意：lightFSM现在作为WheelFSM的嵌套状态机，不再需要单独检查
            // if (gameController.lightFSM == null)
            // {
            //     LogF8.LogWarning("GameFSMCoordinator: LightFSM引用为null");
            // }

            if (gameController.wheelFSM == null)
            {
                LogF8.LogWarning("GameFSMCoordinator: WheelFSM引用为null");
            }
            if (gameController.submarineFSM == null)
            {
                LogF8.LogWarning("GameFSMCoordinator: SubmarineFSM引用为null");
            }

            // 打印所有状态机的当前状态，便于调试和验证
            LogF8.Log($"GameFSMCoordinator: 当前游戏状态: {currentState}");
            LogF8.Log($"GameFSMCoordinator: 当前转盘状态: {gameController.wheelFSM?.CurrentStateId?.ToString() ?? "未初始化"}");
            LogF8.Log($"GameFSMCoordinator: 当前潜艇状态: {gameController.submarineFSM?.CurrentStateId?.ToString() ?? "未初始化"}");
            LogF8.Log($"GameFSMCoordinator: 当前灯光状态: 由WheelFSM嵌套管理");

            // 验证特定的状态组合是否合理
            var wheelState = gameController.wheelFSM?.CurrentStateId?.ToString();
            var submarineState = gameController.submarineFSM?.CurrentStateId?.ToString();

            // 检查不合理的状态组合
            if (submarineState == SubmarineStateType.Moving.ToString() &&
                wheelState == WheelStateType.Spinning.ToString())
            {
                LogF8.LogWarning("GameFSMCoordinator: 检测到不合理状态组合 - 潜艇移动时转盘还在旋转");
            }

            // 注释掉灯光状态检查，因为现在灯光状态是嵌套的
            // if (lightState == LightStateType.Activated.ToString() &&
            //     wheelState == WheelStateType.Spinning.ToString())
            // {
            //     LogF8.LogWarning("GameFSMCoordinator: 检测到可能的状态异常 - 灯光已激活但转盘正在旋转");
            // }
        }

        /// <summary>
        /// 处理游戏状态切换请求事件
        /// </summary>
        private void OnGameStateChangeRequest(GameStateChangeRequestEvent evt)
        {
            if (!isInitialized) return;

            LogF8.Log($"GameFSMCoordinator: 收到游戏状态切换请求 - 目标状态: {evt.RequestedState}");

            // 根据请求切换到目标状态
            ChangeState(evt.RequestedState);
        }

        /// <summary>
        /// 处理LightFSM状态变化
        /// </summary>
        private void HandleLightFSMStateChanged(string previousState, string currentState)
        {
            // 灯光状态变化不再需要直接触发转盘旋转
            // 转盘会在闲置状态下自动检查缓存次数并驱动旋转
            LogF8.Log($"GameFSMCoordinator: 灯光状态变化 - 从 {previousState} 到 {currentState}");
        }

        /// <summary>
        /// 处理WheelFSM状态变化
        /// </summary>
        private void HandleWheelFSMStateChanged(string previousState, string currentState)
        {
            // 移除原来的逻辑：转盘从旋转状态切换到闪烁状态时立即切换到潜艇移动状态
            // 现在的流程是：转盘闪烁完成后，WheelSystem.OnFlashingCompleted会发送MoveSubmarineEvent
            // 然后SubmarineMovingHState会处理这个事件并执行实际的移动

            // 如果需要在转盘状态变化时进行其他处理，可以在这里添加
            LogF8.Log($"GameFSMCoordinator.HandleWheelFSMStateChanged: 转盘状态变化 - 从 {previousState} 到 {currentState}");

            // 注释掉原来的逻辑，避免过早切换到SubmarineMoving状态
            // if (previousState == WheelStateType.Spinning.ToString() && currentState == WheelStateType.Flashing.ToString())
            // {
            //     LogF8.Log("GameFSMCoordinator: 转盘已停止，切换到潜艇移动状态");
            //     ChangeState(GameStateType.SubmarineMoving);
            // }
        }

        /// <summary>
        /// 处理SubmarineFSM状态变化
        /// </summary>
        private void HandleSubmarineFSMStateChanged(string previousState, string currentState)
        {
            // 如果潜艇从到达状态切换到闲置状态，则将轮盘切换到闲置状态并检查缓存
            if (previousState == SubmarineStateType.Arrived.ToString() && currentState == SubmarineStateType.Idle.ToString())
            {
                LogF8.Log("GameFSMCoordinator: 潜艇从到达状态回到闲置，将轮盘切换到闲置状态");
                RequestWheelStateChange(WheelStateType.Idle);

                // 延迟检查转盘缓存，确保wheel状态切换完成
                StartCoroutine(CheckWheelBufferDelayed());
            }
            // 如果潜艇从移动状态切换到到达状态，则切换到闲置状态
            else if (previousState == SubmarineStateType.Moving.ToString() && currentState == SubmarineStateType.Arrived.ToString())
            {
                LogF8.Log("GameFSMCoordinator: 潜艇已到达，切换到闲置状态");
                ChangeState(GameStateType.Idle);
            }
        }

        /// <summary>
        /// 延迟检查转盘缓存并开始新的游戏周期
        /// </summary>
        private IEnumerator CheckWheelBufferDelayed()
        {
            // 等待一帧，确保wheel状态切换完成
            yield return null;

            LogF8.Log("GameFSMCoordinator: 检查转盘缓存，准备开始新的游戏周期");

            // 获取转盘缓存系统
            var wheelBufferSystem = gameController.GetSystem<WheelBufferSystem>();
            if (wheelBufferSystem != null && wheelBufferSystem.HasBufferedSpins())
            {
                LogF8.Log($"GameFSMCoordinator: 检测到转盘缓存次数({wheelBufferSystem.GetBufferedSpinCount()})，开始新的游戏周期");

                // 驱动转盘旋转
                if (wheelBufferSystem.DriveWheelSpinning())
                {
                    LogF8.Log("GameFSMCoordinator: 成功驱动转盘旋转，请求切换到旋转状态");
                    RequestWheelStateChange(WheelStateType.Spinning);
                }
                else
                {
                    LogF8.Log("GameFSMCoordinator: 驱动转盘旋转失败");
                }
            }
            else
            {
                LogF8.Log("GameFSMCoordinator: 没有转盘缓存次数，保持闲置状态");
            }
        }

        /// <summary>
        /// 切换游戏状态
        /// </summary>
        public void ChangeState(GameStateType newState)
        {
            if (!isInitialized)
            {
                LogF8.LogError("GameFSMCoordinator: 尚未初始化，无法切换状态");
                return;
            }

            if (currentState == newState)
            {
                LogF8.LogWarning($"GameFSMCoordinator: 当前已经是 {newState} 状态，不需要切换");
                return;
            }

            GameStateType previousState = currentState;
            currentState = newState;

            LogF8.Log($"GameFSMCoordinator: 状态变化 - 从 {previousState} 到 {currentState}");

            // 发送游戏状态变化事件
            TypeEventSystem.Global.Send(new GameStateChangedEvent
            {
                PreviousState = previousState,
                CurrentState = currentState
            });

            // 通知GameController
            gameController.SetGameState(currentState);

            // 协调其他FSM的状态变化
            CoordinateOtherFSMs(previousState, currentState);
        }

        /// <summary>
        /// 协调其他FSM的状态变化
        /// 所有状态切换逻辑都在这里统一处理
        /// </summary>
        private void CoordinateOtherFSMs(GameStateType previousState, GameStateType currentState)
        {
            LogF8.Log($"GameFSMCoordinator.CoordinateOtherFSMs: 协调状态切换 - 从 {previousState} 到 {currentState}");

            // 移除冗余的WheelFSM状态切换，因为：
            // 1. WheelFSM自己已经在监听FlashingLights状态并自动切换到Idle
            // 2. wheel的状态切换现在由潜艇状态变化驱动（潜艇从arrived到idle时）
            if (previousState == GameStateType.Idle && currentState == GameStateType.FlashingLights)
            {
                LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: 灯光闪烁状态，WheelFSM将自动处理状态切换");
            }
            // 如果从闲置状态切换到转盘旋转状态，切换WheelFSM到旋转状态
            else if (previousState == GameStateType.Idle && currentState == GameStateType.WheelSpinning)
            {
                var wheelFSM = gameController.wheelFSM;
                if (wheelFSM != null)
                {
                    LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: 切换WheelFSM到旋转状态");
                    wheelFSM.ChangeState(WheelStateType.Spinning);
                }
                else
                {
                    LogF8.LogError("GameFSMCoordinator.CoordinateOtherFSMs: WheelFSM引用为null");
                }
            }
            // 如果从转盘旋转状态切换到潜艇移动状态，则切换SubmarineFSM到移动状态
            else if (previousState == GameStateType.WheelSpinning && currentState == GameStateType.SubmarineMoving)
            {
                LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: 转盘旋转完成，开始潜艇移动");

                // 改变潜艇状态为移动状态
                var submarineFSM = gameController.submarineFSM;
                if (submarineFSM != null)
                {
                    submarineFSM.ChangeState(SubmarineStateType.Moving);

                    // 注释掉延迟发送移动事件的调用，因为现在WheelSystem.OnFlashingCompleted会直接发送MoveSubmarineEvent
                    // 这样可以避免重复调用MoveSubmarine方法
                    LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: 潜艇状态已切换到Moving，等待WheelSystem发送的MoveSubmarineEvent事件");

                    // 移除重复的移动调用
                    // StartCoroutine(SendMoveSubmarineEventDelayed());
                }
                else
                {
                    LogF8.LogError("GameFSMCoordinator.CoordinateOtherFSMs: SubmarineFSM引用为null");
                }
            }
            // 如果从潜艇移动状态切换到闲置状态，则切换SubmarineFSM到闲置状态
            else if (previousState == GameStateType.SubmarineMoving && currentState == GameStateType.Idle)
            {
                LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: 协调SubmarineFSM切换到闲置状态");
                var submarineFSM = gameController.submarineFSM;
                if (submarineFSM != null)
                {
                    // 只有在SubmarineFSM不在Idle状态时才切换
                    if (submarineFSM.CurrentStateId != SubmarineStateType.Idle)
                    {
                        submarineFSM.ChangeState(SubmarineStateType.Idle);
                    }
                }
                else
                {
                    LogF8.LogError("GameFSMCoordinator.CoordinateOtherFSMs: SubmarineFSM引用为null");
                }

                // 移除冗余的WheelFSM状态切换，因为：
                // 1. wheel的状态切换现在由潜艇状态变化驱动（潜艇从arrived到idle时）
                // 2. wheel的状态应该由其自身逻辑控制（如WheelFlashingHState完成后自动切换）
                LogF8.Log("GameFSMCoordinator.CoordinateOtherFSMs: WheelFSM将自动处理状态切换和缓存检查");
            }
        }

        /// <summary>
        /// 获取当前游戏状态
        /// </summary>
        public GameStateType GetCurrentState()
        {
            return currentState;
        }

        /// <summary>
        /// 从转盘结果获取移动步数
        /// </summary>
        private int GetStepsFromWheelResult(WheelResult result)
        {
            switch (result)
            {
                case WheelResult.Stop:
                    return 0;
                case WheelResult.Move1:
                    return 1;
                case WheelResult.Move2:
                    return 2;
                case WheelResult.Move3:
                    return 3;
                case WheelResult.Move4:
                    return 4;
                default:
                    LogF8.LogWarning($"GameFSMCoordinator: 未知的转盘结果：{result}，返回0");
                    return 0;
            }
        }

        /// <summary>
        /// 延迟发送潜艇移动事件的协程
        /// 修改为直接调用SubmarineManager，消除事件链
        /// </summary>
        private IEnumerator SendMoveSubmarineEventDelayed()
        {
            LogF8.Log("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 开始延迟处理潜艇移动");

            // 等待一帧，确保状态转换完成
            yield return null;

            LogF8.Log("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 延迟完成，开始获取转盘结果");

            // 获取转盘结果
            var wheelModel = gameController.GetSystem<WheelSystem>().GetModel<WheelModel>();
            if (wheelModel != null)
            {
                WheelResult wheelResult = wheelModel.TargetResult.Value;
                int steps = GetStepsFromWheelResult(wheelResult);

                LogF8.Log($"[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 转盘结果: {wheelResult}，移动步数: {steps}");

                // 直接调用SubmarineManager，消除事件链
                if (SubmarineManager.Instance != null)
                {
                    LogF8.Log($"[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 直接调用SubmarineManager.MoveSubmarine，步数: {steps}");

                    // 直接调用移动方法，传入完成回调
                    SubmarineManager.Instance.MoveSubmarine(steps, () => {
                        LogF8.Log("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 潜艇移动完成回调");
                        // 移动完成后的处理可以在这里进行
                        // 注意：状态切换已经在SubmarineManager中处理了
                    });
                }
                else
                {
                    LogF8.LogError("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: SubmarineManager.Instance为null，无法执行移动");

                    // 降级方案：如果SubmarineManager不可用，仍然发送事件
                    LogF8.LogWarning("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 使用降级方案，发送MoveSubmarineEvent事件");
                    var moveEvent = new MoveSubmarineEvent { Steps = steps };
                    TypeEventSystem.Global.Send(moveEvent);
                }
            }
            else
            {
                LogF8.LogError("[GameFSMCoordinator] SendMoveSubmarineEventDelayed: 无法获取WheelModel");
            }
        }

        private void OnDestroy()
        {
            // 取消注册FSM状态变化事件监听
            TypeEventSystem.Global.UnRegister<FSMStateChangedEvent>(OnFSMStateChanged);

            // 取消注册游戏状态切换请求事件监听
            TypeEventSystem.Global.UnRegister<GameStateChangeRequestEvent>(OnGameStateChangeRequest);

            // 清理单例引用
            if (_instance == this)
            {
                _instance = null;
            }
        }

        /// <summary>
        /// 实现ICanGetSystem接口
        /// </summary>
        public IArchitecture GetArchitecture()
        {
            return gameController.GetArchitecture();
        }

        #region 公共接口方法
        /// <summary>
        /// 请求切换游戏状态（公共接口）
        /// </summary>
        /// <param name="targetState">目标状态</param>
        public void RequestStateChange(GameStateType targetState)
        {
            LogF8.Log($"GameFSMCoordinator: 收到状态切换请求 - 目标状态: {targetState}");
            ChangeState(targetState);
        }

        /// <summary>
        /// 请求切换灯光状态（公共接口）
        /// </summary>
        /// <param name="targetState">目标灯光状态</param>
        public void RequestLightStateChange(LightStateType targetState)
        {
            LogF8.Log($"GameFSMCoordinator: 收到灯光状态切换请求 - 目标状态: {targetState}");

            // 通过WheelFSM的嵌套LightFSM来切换灯光状态
            var wheelFSM = gameController?.wheelFSM;
            if (wheelFSM != null)
            {
                // 确保WheelFSM处于Idle状态，这样才能访问嵌套的LightFSM
                if (wheelFSM.CurrentStateId != WheelStateType.Idle)
                {
                    LogF8.LogWarning("GameFSMCoordinator: WheelFSM不在Idle状态，无法切换灯光状态");
                    return;
                }

                // 通过WheelFSM的IdleState来切换嵌套的LightFSM状态
                // 这需要WheelFSM提供相应的接口
                LogF8.Log($"GameFSMCoordinator: 通过WheelFSM切换嵌套LightFSM到状态: {targetState}");
            }
            else
            {
                LogF8.LogError("GameFSMCoordinator: WheelFSM引用为null，无法切换灯光状态");
            }
        }

        /// <summary>
        /// 请求切换转盘状态（公共接口）
        /// </summary>
        /// <param name="targetState">目标转盘状态</param>
        public void RequestWheelStateChange(WheelStateType targetState)
        {
            LogF8.Log($"GameFSMCoordinator: 收到转盘状态切换请求 - 目标状态: {targetState}");

            var wheelFSM = gameController?.wheelFSM;
            if (wheelFSM != null)
            {
                // 检查当前状态，避免不必要的切换
                if (wheelFSM.CurrentStateId == targetState)
                {
                    LogF8.Log($"GameFSMCoordinator: 转盘已经在目标状态 {targetState}，无需切换");
                    return;
                }

                LogF8.Log($"GameFSMCoordinator: 转盘状态切换 - 从 {wheelFSM.CurrentStateId} 到 {targetState}");

                // 根据转盘状态切换，同时更新游戏状态
                switch (targetState)
                {
                    case WheelStateType.Spinning:
                        ChangeState(GameStateType.WheelSpinning);
                        break;
                    case WheelStateType.Flashing:
                        // 切换到闪烁状态，直接切换WheelFSM状态
                        LogF8.Log("GameFSMCoordinator: 切换WheelFSM到闪烁状态");
                        wheelFSM.ChangeState(targetState);
                        break;
                    case WheelStateType.Idle:
                        // 转盘回到闲置状态通常意味着游戏回到闲置状态
                        if (currentState != GameStateType.Idle)
                        {
                            ChangeState(GameStateType.Idle);
                        }
                        else
                        {
                            // 游戏状态已经是Idle，只需要切换转盘FSM状态
                            LogF8.Log("GameFSMCoordinator: 游戏状态已经是Idle，直接切换转盘FSM状态");
                            wheelFSM.ChangeState(targetState);
                        }
                        break;
                    default:
                        // 其他状态直接切换，不改变游戏状态
                        wheelFSM.ChangeState(targetState);
                        break;
                }
            }
            else
            {
                LogF8.LogError("GameFSMCoordinator: WheelFSM引用为null，无法切换转盘状态");
            }
        }

        /// <summary>
        /// 请求切换潜艇状态（公共接口）
        /// </summary>
        /// <param name="targetState">目标潜艇状态</param>
        public void RequestSubmarineStateChange(SubmarineStateType targetState)
        {
            LogF8.Log($"GameFSMCoordinator: 收到潜艇状态切换请求 - 目标状态: {targetState}");

            var submarineFSM = gameController?.submarineFSM;
            if (submarineFSM != null)
            {
                // 检查潜艇FSM是否已经在目标状态
                if (submarineFSM.CurrentStateId == targetState)
                {
                    LogF8.Log($"GameFSMCoordinator: 潜艇FSM已经在目标状态 {targetState}，无需切换");
                    return;
                }

                // 根据潜艇状态切换，同时更新游戏状态
                switch (targetState)
                {
                    case SubmarineStateType.Moving:
                        ChangeState(GameStateType.SubmarineMoving);
                        break;
                    case SubmarineStateType.Idle:
                        // 潜艇回到闲置状态通常意味着游戏回到闲置状态
                        if (currentState != GameStateType.Idle)
                        {
                            ChangeState(GameStateType.Idle);
                        }
                        else
                        {
                            // 游戏状态已经是Idle，只需要切换潜艇FSM状态
                            LogF8.Log("GameFSMCoordinator: 游戏状态已经是Idle，直接切换潜艇FSM状态");
                            submarineFSM.ChangeState(targetState);
                        }
                        break;
                    default:
                        // 其他状态直接切换，不改变游戏状态
                        submarineFSM.ChangeState(targetState);
                        break;
                }
            }
            else
            {
                LogF8.LogError("GameFSMCoordinator: SubmarineFSM引用为null，无法切换潜艇状态");
            }
        }
        #endregion
    }
}
